---
name: Support issue
about: Create a support issue or a bug report
title: ''
labels: ''
assignees: ''
---

# Read this before submitting your support ticket/bug report

- [ ] Make sure that you have searched for your problem in the **FAQ**: <https://github.com/solidify/jira-azuredevops-migrator/blob/master/docs/faq.md>
- [ ] Make sure that you have searched for your problem in the **issue board** (you can search for keywords in the **filters** search bar): <https://github.com/solidify/jira-azuredevops-migrator/issues?q=is%3Aissue+>
- [ ] Fill in the issue template below (starting with **Describe the problem**)
- [ ] Delete this section but keep the issue template
---

**Describe the problem**

A clear and concise description of what the problem/bug is.

**To Reproduce**

Steps to reproduce the behavior:

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Tool version**
 - Version of the jira-azuredevops-migrator tool [e.g. 2.3.117]

**Attachments**

Please attach the following files:

- config.json
- jira-export-log.txt
- wi-import-log.txt

**Screenshots**
If applicable, add screenshots to help explain your problem.
