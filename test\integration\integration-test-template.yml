steps:
- checkout: self

- task: PowerShell@2
  displayName: 'Replace Migration workspace token in config'
  inputs:
    targetType: 'inline'
    script: |
      $file = "$(System.DefaultWorkingDirectory)\test\integration\$(ConfigFileName)"
      $str_find = "__workspace__"
      $str_replace = "$(System.DefaultWorkingDirectory)\workspace"
      $str_replace = $str_replace -replace "\\", "\\"
      ((Get-Content -path $file -Raw) -replace $str_find, $str_replace) | Set-Content -Path $file
      cat $file

- task: NuGetCommand@2
  displayName: 'NuGet restore'
  inputs:
    restoreSolution: '**\*.sln'

- task: VSBuild@1
  displayName: 'Build solution WorkItemMigrator'
  inputs:
    solution: $(System.DefaultWorkingDirectory)\src\WorkItemMigrator\WorkItemMigrator.sln
    platform: '$(BuildPlatform)'
    configuration: '$(BuildConfiguration)'

- task: CopyFiles@2
  displayName: 'Copy JiraExport files'
  inputs:
    SourceFolder: '$(system.defaultworkingdirectory)\src\WorkItemMigrator\JiraExport\bin\$(BuildConfiguration)\net6'
    Contents: |
        ApplicationInsights.config
        Atlassian.Jira.dll
        jira-export.exe
        jira-export.dll
        jira-export.exe.config
        jira-export.runtimeconfig.json
        Microsoft.AI.Agent.Intercept.dll
        Microsoft.AI.DependencyCollector.dll
        Microsoft.ApplicationInsights.dll
        Microsoft.Extensions.CommandLineUtils.dll
        Migration.Common.dll
        Migration.Common.Log.dll
        Migration.WIContract.dll
        Newtonsoft.Json.dll
        RestSharp.dll
        Semver.dll
        System.Configuration.ConfigurationManager.dll
        System.Diagnostics.DiagnosticSource.dll
    TargetFolder: '$(build.artifactstagingdirectory)\WorkItemMigrator'
    flattenFolders: true

- task: CopyFiles@2
  displayName: 'Copy WorkItemImport files'
  inputs:
    SourceFolder: '$(system.defaultworkingdirectory)\src\WorkItemMigrator\WorkItemImport\bin\$(BuildConfiguration)\net6'
    Contents: |
        Microsoft.TeamFoundation.Client.dll
        Microsoft.TeamFoundation.Common.dll
        Microsoft.TeamFoundation.Core.WebApi.dll
        Microsoft.TeamFoundation.SourceControl.WebApi.dll
        Microsoft.TeamFoundation.WorkItemTracking.Client.DataStoreLoader.dll
        Microsoft.TeamFoundation.WorkItemTracking.Client.dll
        Microsoft.TeamFoundation.WorkItemTracking.Common.dll
        Microsoft.TeamFoundation.WorkItemTracking.Proxy.dll
        Microsoft.TeamFoundation.WorkItemTracking.WebApi.dll
        Microsoft.VisualStudio.Services.Common.dll
        Microsoft.VisualStudio.Services.WebApi.dll
        Microsoft.WITDataStore32.dll
        System.Net.Http.dll
        System.Net.Http.Formatting.dll
        wi-import.exe
        wi-import.exe.config
        wi-import.dll
        wi-import.runtimeconfig.json
    TargetFolder: '$(build.artifactstagingdirectory)\WorkItemMigrator'
    flattenFolders: true

- script: pip install requests python-dateutil
  displayName: pip install

- task: PythonScript@0
  displayName: Delete work items on target org, PythonScript
  inputs:
    scriptSource: 'filePath'
    scriptPath: '$(System.DefaultWorkingDirectory)\test\integration\delete-work-items.py'
    arguments: '$(AdoOrganizationUrl) $(AdoProjectName) $(AdoApiToken)'

- task: PowerShell@2
  displayName: Create workspace and copy users.txt
  inputs:
    targetType: 'inline'
    script: |
      New-Item -Path "$(System.DefaultWorkingDirectory)" -Name "workspace" -ItemType Directory
      Copy-Item "$(System.DefaultWorkingDirectory)\test\integration\users.txt" -Destination "$(System.DefaultWorkingDirectory)\workspace"

- script: $(build.artifactstagingdirectory)\WorkItemMigrator\jira-export.exe -u $(JiraUser) $(JiraAuthFlag) $(JiraApiToken) --url $(JiraUrl) --config $(System.DefaultWorkingDirectory)\test\integration\$(ConfigFileName) --force
  displayName: jira-export.exe

- script: $(build.artifactstagingdirectory)\WorkItemMigrator\wi-import.exe --token $(AdoApiToken) --url $(AdoOrganizationUrl) --config $(System.DefaultWorkingDirectory)\test\integration\$(ConfigFileName) --force
  displayName: wi-import.exe

- task: PythonScript@0
  displayName: Smoke tests, PythonScript
  inputs:
    scriptSource: 'filePath'
    scriptPath: '$(System.DefaultWorkingDirectory)\test\integration\smoke-tests.py'
    arguments: '$(AdoOrganizationUrl) $(AdoProjectName) $(AdoApiToken) $(JiraUrl) $(JiraUser) $(JiraApiToken) $(JiraProject) $(System.DefaultWorkingDirectory)\test\integration\users.txt $(AuthMethod)'
