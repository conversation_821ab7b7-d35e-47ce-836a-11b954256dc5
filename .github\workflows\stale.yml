# This workflow warns and then closes issues that have had no activity for a specified amount of time.

# For more information, see:
# https://github.com/actions/stale
name: Mark as stale/close inactive issues

on:
  schedule:
  - cron: '30 17 * * *'

jobs:
  stale:

    runs-on: ubuntu-latest
    permissions:
      issues: write

    steps:
    - uses: actions/stale@v5
      with:
        any-of-labels: 'support'
        days-before-issue-stale: 25
        days-before-issue-close: 5
        stale-issue-label: "stale"
        stale-issue-message: "This issue is stale because it has been open for 25 days with no activity."
        close-issue-message: "This issue was closed because it has been inactive for 5 days since being marked as stale."
        days-before-pr-stale: -1
        days-before-pr-close: -1
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        operations-per-run: 45
