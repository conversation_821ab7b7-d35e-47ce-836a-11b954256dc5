name: Sync issue to Azure DevOps work item

on:
  issues:
    types:
      [opened, edited, deleted, closed, reopened, labeled, unlabeled, assigned]

jobs:
  alert:
    runs-on: ubuntu-latest
    steps:
      - uses: danhellem/github-actions-issue-to-work-item@master
        env:
          ado_token: "${{ secrets.ADO_PERSONAL_ACCESS_TOKEN }}"
          github_token: "${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}"
          ado_organization: "solidify"
          ado_project: "Internal"
          ado_area_path: "Internal\\Portfolio\\SRE"
          ado_iteration_path: "Internal"
          ado_wit: "Product Backlog Item"
          ado_new_state: "New"
          ado_active_state: "Committed"
          ado_close_state: "Done"
          ado_bypassrules: true
