﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6</TargetFramework>
    <AssemblyTitle>Migration.WIContract</AssemblyTitle>
    <Product>Migration.WIContract</Product>
    <Copyright>Copyright (c) Solidify</Copyright>
    <Deterministic>false</Deterministic>
    <Version>0.0.1</Version>
    <AssemblyVersion>1.0.*</AssemblyVersion>
    <FileVersion>1.0.*</FileVersion>
    <OutputPath>bin\$(Configuration)\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Migration.Common.Log\Migration.Common.Log.csproj" />
  </ItemGroup>
</Project>