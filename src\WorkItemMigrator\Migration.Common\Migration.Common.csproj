﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6</TargetFramework>
    <AssemblyTitle>Migration.Common</AssemblyTitle>
    <Product>Migration.Common</Product>
    <Copyright>Copyright (c) Solidify</Copyright>
    <Deterministic>false</Deterministic>
    <Version>0.0.1</Version>
    <AssemblyVersion>1.0.*</AssemblyVersion>
    <FileVersion>1.0.*</FileVersion>
    <OutputPath>bin\$(Configuration)\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="7.0.2" />
    <PackageReference Include="System.ValueTuple" Version="4.5.0" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Management" />
    <Reference Include="System.Web" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Migration.Common.Log\Migration.Common.Log.csproj" />
    <ProjectReference Include="..\Migration.WIContract\Migration.WIContract.csproj" />
  </ItemGroup>
</Project>