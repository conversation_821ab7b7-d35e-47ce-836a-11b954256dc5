﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6</TargetFramework>
    <RootNamespace>Migration.Jira_Export.Tests</RootNamespace>
    <IsPackable>false</IsPackable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Atlassian.SDK" Version="13.0.0" />
    <PackageReference Include="AutoFixture" Version="4.18.0" />
    <PackageReference Include="AutoFixture.AutoNSubstitute" Version="4.18.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NSubstitute" Version="5.1.0" />
    <PackageReference Include="NUnit" Version="3.13.3" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.7.2" />
    <PackageReference Include="Microsoft.TeamFoundationServer.Client" Version="16.205.1" />
    <PackageReference Include="System.IO.Abstractions" Version="19.2.69" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\JiraExport\jira-export.csproj" />
    <ProjectReference Include="..\..\Migration.Common\Migration.Common.csproj" />
    <ProjectReference Include="..\..\Migration.WIContract\Migration.WIContract.csproj" />
  </ItemGroup>
</Project>