﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <RootNamespace>JiraExport</RootNamespace>
    <TargetFramework>net6</TargetFramework>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>C:\temp\jirapublish\</PublishUrl>
    <PublishSingleFile>true</PublishSingleFile>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <AutorunEnabled>true</AutorunEnabled>
    <ApplicationRevision>1</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <AssemblyTitle>jira-export</AssemblyTitle>
    <Product>Jira Exporter</Product>
    <Copyright>Copyright (c) Solidify</Copyright>
    <Deterministic>false</Deterministic>
    <Version>0.0.1</Version>
    <AssemblyVersion>1.0.*</AssemblyVersion>
    <FileVersion>1.0.*</FileVersion>
    <OutputPath>bin\$(Configuration)\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>JiraExport.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>7BC5315A5561BBFF6B0FC83E960A4687D212C715</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>JiraExporter_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>false</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>Properties\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Atlassian.SDK" Version="13.0.0" />
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.21.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.Agent.Intercept" Version="2.4.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.DependencyCollector" Version="2.21.0" />
    <PackageReference Include="Microsoft.Extensions.CommandLineUtils" Version="1.1.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RestSharp" Version="106.12.0" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="7.0.2" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="RestSharp, Version=106.12.0.0, Culture=neutral, PublicKeyToken=598062e77f915f75">
      <HintPath>..\packages\RestSharp.106.12.0\lib\netstandard2.0\RestSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Management" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Properties\app.manifest" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Migration.Common.Log\Migration.Common.Log.csproj" />
    <ProjectReference Include="..\Migration.Common\Migration.Common.csproj" />
    <ProjectReference Include="..\Migration.WIContract\Migration.WIContract.csproj" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="jirastyles.css" />
  </ItemGroup>
</Project>