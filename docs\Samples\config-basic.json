{"source-project": "SourceProject", "target-project": "TargetProject", "query": "project=ProjectName ORDER BY Prio ASC", "using-jira-cloud": true, "workspace": "C:\\Temp\\JiraExport\\", "epic-link-field": "Epic Link", "sprint-field": "Sprint", "download-options": 7, "batch-size": 20, "log-level": "Info", "attachment-folder": "Attachments", "user-mapping-file": "users.txt", "base-area-path": "Migrated", "base-iteration-path": "Migrated", "ignore-failed-links": true, "include-link-comments": false, "include-jira-css-styles": false, "ignore-empty-revisions": false, "suppress-notifications": false, "sleep-time-between-revision-import-milliseconds": 0, "process-template": "Basic", "link-map": {"link": [{"source": "Epic", "target": "System.LinkTypes.Hierarchy-Reverse"}, {"source": "Parent", "target": "System.LinkTypes.Hierarchy-Reverse"}, {"source": "Child", "target": "System.LinkTypes.Hierarchy-Forward"}, {"source": "Relates", "target": "System.LinkTypes.Related"}, {"source": "<PERSON><PERSON><PERSON>", "target": "System.LinkTypes.Related"}, {"source": "Duplicate", "target": "System.LinkTypes.Duplicate-Forward"}]}, "type-map": {"type": [{"source": "Epic", "target": "Epic"}, {"source": "New Feature", "target": "Issue"}, {"source": "Task", "target": "Task"}]}, "field-map": {"field": [{"source": "summary", "target": "System.Title", "mapper": "MapTitle"}, {"source": "assignee", "target": "System.AssignedTo", "mapper": "MapUser"}, {"source": "Sprint", "source-type": "name", "target": "System.IterationPath", "mapper": "MapSprint"}, {"source": "description", "target": "System.Description", "not-for": "Bug", "mapper": "MapRendered"}, {"source": "priority", "target": "Microsoft.VSTS.Common.Priority", "mapping": {"values": [{"source": "Highest", "target": "1"}, {"source": "High", "target": "2"}, {"source": "Medium", "target": "3"}, {"source": "Low", "target": "3"}, {"source": "Lowest", "target": "4"}]}}, {"source": "labels", "target": "System.Tags", "mapper": "MapTags"}, {"source": "comment", "target": "System.History", "mapper": "MapRendered"}, {"source": "status", "target": "System.State", "mapping": {"values": [{"source": "To Do", "target": "To Do"}, {"source": "In Progress", "target": "Doing"}, {"source": "Done", "target": "Done"}]}}, {"source": "description", "target": "Microsoft.VSTS.TCM.ReproSteps", "for": "Bug", "mapper": "MapRendered"}]}}